import os

# Database configuration
SQLALCHEMY_DATABASE_URI = '***********************************************/superset'

# Security configuration
SECRET_KEY = 'your-secret-key-here-change-in-production'
WTF_CSRF_ENABLED = True
WTF_CSRF_TIME_LIMIT = None

# Feature flags
FEATURE_FLAGS = {
    'ENABLE_TEMPLATE_PROCESSING': True,
    'DASHBOARD_NATIVE_FILTERS': True,
    'DASHBOARD_CROSS_FILTERS': True,
    'DASHBOARD_RBAC': True,
}

# Cache configuration (simple)
CACHE_CONFIG = {
    'CACHE_TYPE': 'SimpleCache',
    'CACHE_DEFAULT_TIMEOUT': 300
}

# Email configuration (optional)
SMTP_HOST = 'localhost'
SMTP_STARTTLS = True
SMTP_SSL = False
SMTP_USER = 'superset'
SMTP_PORT = 25
SMTP_PASSWORD = 'superset'
SMTP_MAIL_FROM = '<EMAIL>'

# WebDriver configuration for reports
WEBDRIVER_BASEURL = 'http://superset:8088/'
WEBDRIVER_BASEURL_USER_FRIENDLY = 'http://localhost:8088/'

# SQL Lab configuration
SQLLAB_CTAS_NO_LIMIT = True
SQLLAB_TIMEOUT = 300
SQLLAB_DEFAULT_DBID = None

# Dashboard configuration
DASHBOARD_AUTO_REFRESH_MODE = 'fetch'
DASHBOARD_AUTO_REFRESH_INTERVALS = [
    [0, 'Don\'t refresh'],
    [10, '10 seconds'],
    [30, '30 seconds'],
    [60, '1 minute'],
    [300, '5 minutes'],
    [1800, '30 minutes'],
    [3600, '1 hour'],
]

# Logging configuration
ENABLE_TIME_ROTATE = True
TIME_ROTATE_LOG_LEVEL = 'DEBUG'
FILENAME = os.path.join(DATA_DIR, 'superset.log')

# Allow embedding of Superset dashboards in iframes
HTTP_HEADERS = {'X-Frame-Options': 'ALLOWALL'}

# Custom CSS
CUSTOM_CSS = """
.navbar-brand {
    font-weight: bold;
}
"""
