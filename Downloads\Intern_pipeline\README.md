# Data Engineering Pipeline with Docker Compose

This project provides a complete data engineering stack using Docker Compose, including:

- **Apache NiFi** - Data ingestion and flow management
- **HDFS** - Distributed file system
- **Apache Spark** - Big data processing
- **Apache Hive** - Data warehouse
- **Apache Airflow** - Workflow orchestration
- **Apache Superset** - Data visualization and exploration

## Prerequisites

- Docker and Docker Compose installed
- At least 8GB of RAM available for Docker
- Ports 8080, 8081, 8088, 8443, 9000, 9070, 9083, 9864, 9870, 10000 available

## Quick Start

1. **Clone or download this repository**

2. **Make the Airflow entrypoint script executable:**
   ```bash
   chmod +x scripts/airflow-entrypoint.sh
   ```

3. **Start all services:**
   ```bash
   docker-compose up -d
   ```

4. **Wait for services to initialize** (this may take 5-10 minutes on first run)

## Service Access

Once all services are running, you can access them at:

| Service | URL | Username | Password |
|---------|-----|----------|----------|
| **Apache NiFi** | https://localhost:8443/nifi | - | - |
| **HDFS NameNode** | http://localhost:9870 | - | - |
| **Spark Master** | http://localhost:8080 | - | - |
| **Airflow** | http://localhost:8081 | admin | admin |
| **Superset** | http://localhost:8088 | admin | admin |
| **Hive Server** | **************************** | - | - |

## Service Details

### Apache NiFi
- **Purpose**: Data ingestion, transformation, and routing
- **Access**: https://localhost:8443/nifi
- **Note**: Uses self-signed certificate, accept security warning in browser

### HDFS (Hadoop Distributed File System)
- **NameNode UI**: http://localhost:9870
- **Default Port**: 9000
- **Purpose**: Distributed storage for big data

### Apache Spark
- **Master UI**: http://localhost:8080
- **Purpose**: Large-scale data processing
- **Configuration**: 1 master + 1 worker node

### Apache Hive
- **Metastore Port**: 9083
- **HiveServer2 Port**: 10000
- **Purpose**: Data warehouse software for querying large datasets
- **Database**: Uses PostgreSQL as metastore

### Apache Airflow
- **Web UI**: http://localhost:8081
- **Username/Password**: admin/admin
- **Purpose**: Workflow orchestration and scheduling
- **Executor**: CeleryExecutor with Redis broker

### Apache Superset
- **Web UI**: http://localhost:8088
- **Username/Password**: admin/admin
- **Purpose**: Data visualization and exploration
- **Database**: Uses PostgreSQL

## Directory Structure

```
├── docker-compose.yml          # Main Docker Compose configuration
├── hadoop.env                  # Hadoop environment variables
├── superset_config.py         # Superset configuration
├── scripts/
│   └── airflow-entrypoint.sh  # Airflow initialization script
├── dags/                      # Airflow DAGs directory
│   └── sample_dag.py         # Sample DAG
├── logs/                      # Airflow logs
└── plugins/                   # Airflow plugins
```

## Usage Examples

### 1. Upload Data to HDFS
```bash
# Access the namenode container
docker exec -it namenode bash

# Create a directory in HDFS
hdfs dfs -mkdir /user/data

# Upload a file (example)
hdfs dfs -put /path/to/local/file /user/data/
```

### 2. Submit Spark Job
```bash
# Access the spark-master container
docker exec -it spark-master bash

# Submit a Spark job
spark-submit --master spark://spark-master:7077 /path/to/your/spark/job.py
```

### 3. Connect to Hive
```bash
# Access the hive-server container
docker exec -it hive-server bash

# Connect to Hive
beeline -u ****************************
```

### 4. Create Airflow DAG
- Place your DAG files in the `dags/` directory
- They will be automatically picked up by Airflow
- Check the Airflow UI to see and manage your DAGs

### 5. Configure Superset Data Sources
1. Access Superset at http://localhost:8088
2. Go to Settings > Database Connections
3. Add connections to your data sources (Hive, PostgreSQL, etc.)

## Stopping Services

To stop all services:
```bash
docker-compose down
```

To stop and remove all data:
```bash
docker-compose down -v
```

## Troubleshooting

### Common Issues

1. **Services not starting**: Check if required ports are available
2. **Out of memory**: Increase Docker memory allocation
3. **Slow startup**: Services have dependencies; wait for complete initialization
4. **Permission issues**: Ensure proper file permissions, especially for scripts

### Checking Service Status
```bash
# Check all containers
docker-compose ps

# Check logs for a specific service
docker-compose logs [service-name]

# Example: Check Airflow webserver logs
docker-compose logs airflow-webserver
```

### Resource Requirements

Minimum recommended resources:
- **RAM**: 8GB
- **CPU**: 4 cores
- **Disk**: 20GB free space

## Customization

### Adding Custom Configurations
- Modify `hadoop.env` for Hadoop/Hive settings
- Edit `superset_config.py` for Superset customization
- Add environment variables in `docker-compose.yml`

### Scaling Services
- Add more Spark workers by duplicating the spark-worker service
- Scale Airflow workers: `docker-compose up --scale airflow-worker=3`

## Security Notes

This setup is intended for development and testing. For production use:
- Change default passwords
- Configure proper authentication
- Use proper SSL certificates
- Implement network security
- Set up proper backup strategies

## Support

For issues and questions:
1. Check the logs of the specific service
2. Verify all services are running: `docker-compose ps`
3. Ensure sufficient system resources
4. Check port conflicts
