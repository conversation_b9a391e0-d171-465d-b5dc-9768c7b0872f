from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.operators.python import PythonOperator

# Default arguments for the DAG
default_args = {
    'owner': 'data-engineer',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'sample_data_pipeline',
    default_args=default_args,
    description='A sample data pipeline DAG',
    schedule_interval=timedelta(days=1),
    catchup=False,
    tags=['example', 'data-pipeline'],
)

def print_hello():
    """Simple Python function to print hello"""
    print("Hello from the data pipeline!")
    return "Hello World!"

def print_context(ds, **kwargs):
    """Print the Airflow context"""
    print(f"Execution date: {ds}")
    print(f"Context: {kwargs}")
    return "Context printed!"

# Task 1: Print hello
hello_task = PythonOperator(
    task_id='print_hello',
    python_callable=print_hello,
    dag=dag,
)

# Task 2: Print context
context_task = PythonOperator(
    task_id='print_context',
    python_callable=print_context,
    dag=dag,
)

# Task 3: Check HDFS status
check_hdfs = BashOperator(
    task_id='check_hdfs_status',
    bash_command='echo "Checking HDFS status..." && curl -f http://namenode:9870/jmx?qry=Hadoop:service=NameNode,name=NameNodeStatus || echo "HDFS not accessible"',
    dag=dag,
)

# Task 4: Check Spark status
check_spark = BashOperator(
    task_id='check_spark_status',
    bash_command='echo "Checking Spark status..." && curl -f http://spark-master:8080/json/ || echo "Spark not accessible"',
    dag=dag,
)

# Task 5: Final task
final_task = BashOperator(
    task_id='pipeline_complete',
    bash_command='echo "Data pipeline execution completed successfully!"',
    dag=dag,
)

# Define task dependencies
hello_task >> context_task
context_task >> [check_hdfs, check_spark]
[check_hdfs, check_spark] >> final_task
