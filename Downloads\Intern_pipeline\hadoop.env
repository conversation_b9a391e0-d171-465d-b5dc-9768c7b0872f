CORE_CONF_fs_defaultFS=hdfs://namenode:9000
CORE_CONF_hadoop_http_staticuser_user=root
CORE_CONF_hadoop_proxyuser_hue_hosts=*
CORE_CONF_hadoop_proxyuser_hue_groups=*
CORE_CONF_io_compression_codecs=org.apache.hadoop.io.compress.SnappyCodec

HDFS_CONF_dfs_webhdfs_enabled=true
HDFS_CONF_dfs_permissions_enabled=false
HDFS_CONF_dfs_nameservices=hdfscluster
HDFS_CONF_dfs_ha_namenodes_hdfscluster=nn1,nn2
HDFS_CONF_dfs_namenode_rpc_address_hdfscluster_nn1=namenode:9000
HDFS_CONF_dfs_namenode_rpc_address_hdfscluster_nn2=namenode2:9000
HDFS_CONF_dfs_namenode_http_address_hdfscluster_nn1=namenode:9870
HDFS_CONF_dfs_namenode_http_address_hdfscluster_nn2=namenode2:9870
HDFS_CONF_dfs_namenode_shared_edits_dir=qjournal://journalnode1:8485;journalnode2:8485;journalnode3:8485/hdfscluster
HDFS_CONF_dfs_client_failover_proxy_provider_hdfscluster=org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider
HDFS_CONF_dfs_ha_fencing_methods=sshfence
HDFS_CONF_dfs_ha_fencing_ssh_private_key_files=/var/lib/hadoop-hdfs/.ssh/id_rsa
HDFS_CONF_dfs_journalnode_edits_dir=/hadoop/dfs/journal

YARN_CONF_yarn_log___aggregation___enable=true
YARN_CONF_yarn_log_server_url=http://historyserver:8188/applicationhistory/logs/
YARN_CONF_yarn_resourcemanager_recovery_enabled=true
YARN_CONF_yarn_resourcemanager_store_class=org.apache.hadoop.yarn.server.resourcemanager.recovery.FileSystemRMStateStore
YARN_CONF_yarn_resourcemanager_hostname=resourcemanager
YARN_CONF_yarn_resourcemanager_address=resourcemanager:8032
YARN_CONF_yarn_resourcemanager_scheduler_address=resourcemanager:8030
YARN_CONF_yarn_resourcemanager_resource___tracker_address=resourcemanager:8031
YARN_CONF_yarn_timeline___service_enabled=true
YARN_CONF_yarn_timeline___service_generic___application___history_enabled=true
YARN_CONF_yarn_timeline___service_hostname=historyserver
YARN_CONF_mapreduce_map_output_compress=true
YARN_CONF_mapred_map_output_compress_codec=org.apache.hadoop.io.compress.SnappyCodec
YARN_CONF_yarn_nodemanager_resource_memory___mb=1536
YARN_CONF_yarn_scheduler_maximum___allocation___mb=1536
YARN_CONF_yarn_scheduler_minimum___allocation___mb=512
YARN_CONF_yarn_nodemanager_vmem___check___enabled=false
YARN_CONF_yarn_nodemanager_aux___services=mapreduce_shuffle

MAPRED_CONF_mapreduce_framework_name=yarn
MAPRED_CONF_mapred_child_java_opts=-Xmx512m
MAPRED_CONF_mapreduce_map_memory_mb=512
MAPRED_CONF_mapreduce_reduce_memory_mb=1024
MAPRED_CONF_mapreduce_map_java_opts=-Xmx384m
MAPRED_CONF_mapreduce_reduce_java_opts=-Xmx768m
MAPRED_CONF_yarn_app_mapreduce_am_env=HADOOP_MAPRED_HOME=/opt/hadoop-3.2.1/
MAPRED_CONF_mapreduce_map_env=HADOOP_MAPRED_HOME=/opt/hadoop-3.2.1/
MAPRED_CONF_mapreduce_reduce_env=HADOOP_MAPRED_HOME=/opt/hadoop-3.2.1/

HIVE_SITE_CONF_javax_jdo_option_ConnectionURL=*****************************************************
HIVE_SITE_CONF_javax_jdo_option_ConnectionDriverName=org.postgresql.Driver
HIVE_SITE_CONF_javax_jdo_option_ConnectionUserName=hive
HIVE_SITE_CONF_javax_jdo_option_ConnectionPassword=hive
HIVE_SITE_CONF_datanucleus_autoCreateSchema=false
HIVE_SITE_CONF_hive_metastore_uris=thrift://hive-metastore:9083
