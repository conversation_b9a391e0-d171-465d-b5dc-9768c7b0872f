# Docker volumes and data
volumes/
data/

# Logs
logs/
*.log

# Environment files with sensitive data
.env
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# Airflow
airflow.cfg
airflow.db
airflow-webserver.pid
standalone_admin_password.txt

# NiFi
nifi-app.log
nifi-user.log
nifi-bootstrap.log

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Docker
.dockerignore

# Backup files
*.bak
*.backup

# Local development
local/
dev/
