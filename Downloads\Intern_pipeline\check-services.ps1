# Data Pipeline Services Status Checker
Write-Host "=== Data Engineering Pipeline Status ===" -ForegroundColor Green
Write-Host ""

# Function to check if a URL is accessible
function Test-ServiceUrl {
    param($url, $serviceName)
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $serviceName is accessible at $url" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $serviceName returned status $($response.StatusCode) at $url" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $serviceName is not accessible at $url" -ForegroundColor Red
    }
}

# Check Docker Compose status
Write-Host "Docker Compose Services:" -ForegroundColor Cyan
docker-compose ps

Write-Host ""
Write-Host "Service Accessibility Check:" -ForegroundColor Cyan

# Check each service
Test-ServiceUrl "http://localhost:9870" "HDFS NameNode"
Test-ServiceUrl "http://localhost:8080" "Spark Master"
Test-ServiceUrl "https://localhost:8443/nifi" "Apache NiFi"
Test-ServiceUrl "http://localhost:8081" "Apache Airflow"
Test-ServiceUrl "http://localhost:8088" "Apache Superset"

Write-Host ""
Write-Host "Service URLs:" -ForegroundColor Cyan
Write-Host "• HDFS NameNode:  http://localhost:9870" -ForegroundColor White
Write-Host "• Spark Master:   http://localhost:8080" -ForegroundColor White
Write-Host "• Apache NiFi:    https://localhost:8443/nifi" -ForegroundColor White
Write-Host "• Apache Airflow: http://localhost:8081 (admin/admin)" -ForegroundColor White
Write-Host "• Apache Superset: http://localhost:8088 (admin/admin)" -ForegroundColor White
Write-Host "• Hive Server:    ****************************" -ForegroundColor White

Write-Host ""
Write-Host "Note: Some services may take a few minutes to fully initialize." -ForegroundColor Yellow
